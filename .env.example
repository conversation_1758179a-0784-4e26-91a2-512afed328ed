# Environment Variables Template for US Insurance Platform

# Frontend Environment Variables (Required)
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Backend Environment Variables (Required for full-stack deployment)
DATABASE_URL=postgresql://username:password@localhost:5432/insurance_db

# AI Service API Keys (Optional - for AI features)
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_gemini_api_key

# Authentication (if using custom auth)
JWT_SECRET_KEY=your_jwt_secret_key
JWT_ALGORITHM=HS256

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_FOLDER=uploads

# Redis Configuration (if using caching)
REDIS_URL=redis://localhost:6379

# Email Configuration (if using email features)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Production Environment Variables for Vercel:
# Set these in your Vercel dashboard under Project Settings > Environment Variables

# For frontend-only deployment:
# - NEXT_PUBLIC_API_URL (your backend URL)
# - NEXT_PUBLIC_SUPABASE_URL
# - NEXT_PUBLIC_SUPABASE_ANON_KEY

# For full-stack deployment, add all the above variables
