# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js
.next/
out/
frontend/.next/
frontend/out/

# Nuxt.js
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Uploads
uploads/
backend/uploads/*/

# Test files and temporary files
test_*.py
test_*.js
test_*.html
*_test.py
*_test.js
debug_*.py
temp_*.py
temp_*.js

# Development and debug files
comprehensive_test.py
simple_*.py
check_*.py
verify_*.py
create_*.py
reprocess_*.py
revised_*.py

# Performance and report files
*_performance_*.py
*_performance_*.js
*_performance_*.json
*_report_*.py
*_report_*.js
*_report_*.json
*_report_*.md

# Development documentation
developmentplan.md
*_SUMMARY.md
*_TEST_REPORT.md
*_ENHANCEMENTS.md
RESOLVED_ISSUES.md
